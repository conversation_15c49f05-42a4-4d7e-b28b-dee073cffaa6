'use client'

import React, { useState } from 'react'
import { useAppStore, useActiveTab } from '@/lib/store'
import LightBrowser from '@/components/ui/LightBrowser'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import { WelcomeScreen } from '@/components/WelcomeScreen'
// 暂时注释掉新的内容系统，避免客户端导入服务器端模块
// import ContentRenderer from '@/components/content/ContentRenderer'
// import { ContentType } from '@/lib/content/types'

const WorkArea: React.FC = () => {
  const {
    tabs,
    addTab,
    setProcessing
  } = useAppStore()
  const activeTab = useActiveTab()
  const [inputValue, setInputValue] = useState('')

  // 处理输入提交 - 暂时使用旧系统，避免客户端导入问题
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim()) return

    setProcessing(true)

    try {
      // 判断输入类型并规范化URL
      let processedInput = inputValue.trim()
      let isUrl = false

      // 检测和规范化URL
      if (processedInput.startsWith('http://') || processedInput.startsWith('https://')) {
        isUrl = true
      } else if (processedInput.includes('.') && !processedInput.includes(' ') && processedInput.length > 3) {
        // 可能是没有协议的URL，自动添加https://
        processedInput = 'https://' + processedInput
        isUrl = true
      }

      if (isUrl) {
        // 对于URL：立即创建标签页并显示浏览器，同时后台处理AI分析
        const tabId = addTab({
          title: new URL(processedInput).hostname,
          sourceType: 'url',
          sourceData: processedInput,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false, // 立即显示浏览器，不需要等待
          aiAnalyzing: true // 新增状态：表示AI正在后台分析
        })

        setInputValue('')
        setProcessing(false)

        // 后台异步处理AI分析
        processUrlInBackground(processedInput, tabId)
      } else {
        // 对于文本：使用流式处理
        const tabId = addTab({
          title: '文本内容',
          sourceType: 'text',
          sourceData: inputValue,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false,
          aiAnalyzing: true // 流式分析
        })

        setInputValue('')
        setProcessing(false)

        // 后台流式处理文本内容
        processContentWithStream(processedInput, 'text', tabId)
      }
    } catch (error) {
      console.error('处理错误:', error)
      setProcessing(false)
      // 可以在这里显示错误提示
    }
  }

  // 流式处理内容分析（支持URL和文本）- 保持向后兼容
  const processContentWithStream = async (input: string, type: 'url' | 'text', tabId: string) => {
    try {
      console.log('开始流式AI分析:', input)

      const response = await fetch('/api/process/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input,
          type
        }),
        signal: AbortSignal.timeout(60000) // 60秒超时，给AI生成更多时间
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      let accumulatedAINote = ''
      const decoder = new TextDecoder()
      let basicInfoReceived = false
      let lastUpdateTime = 0
      let pendingUpdate = false
      const UPDATE_THROTTLE = 50 // 优化更新频率为50ms，提升响应性
      const BATCH_SIZE = 10 // 批量处理字符数

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              // 完成流式更新
              const finalTab = useAppStore.getState().tabs.find(t => t.id === tabId)
              useAppStore.getState().updateTab(tabId, {
                aiNoteMarkdown: finalTab?.aiNoteMarkdown || accumulatedAINote,
                aiAnalyzing: false
              })
              return
            }

            try {
              const parsed = JSON.parse(data)

              if (parsed.type === 'basic_info') {
                // 处理基本信息
                const basicInfo = parsed.data
                useAppStore.getState().updateTab(tabId, {
                  title: basicInfo.title || (type === 'url' ? new URL(input).hostname : '文本内容'),
                  originalContent: basicInfo.content
                })
                basicInfoReceived = true

              } else if (parsed.type === 'ai_note' && basicInfoReceived) {
                // 处理结构化笔记流式更新
                const noteData = parsed.data

                if (noteData.content) {
                  accumulatedAINote += noteData.content

                  // 优化的节流更新：使用requestAnimationFrame和批量处理
                  const now = Date.now()
                  if (now - lastUpdateTime >= UPDATE_THROTTLE || noteData.isComplete) {
                    if (!pendingUpdate) {
                      pendingUpdate = true
                      requestAnimationFrame(() => {
                        useAppStore.getState().setStreamingNote(tabId, accumulatedAINote)
                        lastUpdateTime = Date.now()
                        pendingUpdate = false
                      })
                    }
                  }
                }

                if (noteData.isComplete) {
                  // 完成流式生成，转移到最终状态
                  useAppStore.getState().updateTab(tabId, {
                    aiNoteMarkdown: noteData.fullContent || accumulatedAINote,
                    aiAnalyzing: false
                  })
                  // 清空流式状态
                  useAppStore.getState().clearStreamingNote(tabId)
                }
              }
            } catch (e) {
              // 忽略解析错误，继续处理下一行
            }
          }
        }
      }
    } catch (error) {
      console.error('流式结构化分析失败:', error)

      // 更详细的错误处理
      let errorMessage = '分析失败'
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = '请求超时，请稍后重试'
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = '网络连接失败，请检查网络'
        } else {
          errorMessage = error.message
        }
      }

      // 更新状态并显示错误信息
      useAppStore.getState().updateTab(tabId, {
        aiAnalyzing: false,
        error: errorMessage
      })
    }
  }

  // 后台处理URL的AI分析（保持向后兼容）
  const processUrlInBackground = async (url: string, tabId: string) => {
    await processContentWithStream(url, 'url', tabId)
  }

  // 如果没有标签页，显示输入界面
  if (tabs.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="max-w-3xl w-full px-8">
          {/* 极简标题区域 */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mb-6 shadow-lg">
              <span className="text-2xl">✨</span>
            </div>
            <h1 className="text-4xl font-light text-gray-900 mb-3 tracking-tight">
              沉淀
            </h1>
            <p className="text-lg text-gray-500 font-light">
              智能内容分析与结构化笔记生成
            </p>
          </div>

          {/* 现代化输入区域 */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative group">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="输入网页链接或粘贴文本内容..."
                className="w-full h-32 px-6 py-4 bg-white border border-gray-200 rounded-2xl resize-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 outline-none text-base transition-all duration-200 shadow-sm hover:shadow-md group-hover:border-gray-300"
                disabled={useAppStore.getState().isProcessing}
              />
              <div className="absolute bottom-4 right-4 flex items-center space-x-2">
                <div className="text-xs text-gray-400">
                  {inputValue.length > 0 && `${inputValue.length} 字符`}
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <button
                type="submit"
                disabled={!inputValue.trim() || useAppStore.getState().isProcessing}
                className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
              >
                {useAppStore.getState().isProcessing ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>处理中...</span>
                  </div>
                ) : (
                  '开始分析'
                )}
              </button>
            </div>
          </form>


        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 内容区域 - 修复滚动问题 */}
      <div className="flex-1 overflow-hidden">
        {activeTab ? (
          activeTab.isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">正在处理内容...</p>
              </div>
            </div>
          ) : activeTab.sourceData ? (
            activeTab.sourceType === 'url' ? (
              // 对于URL类型，显示轻量级浏览器
              <div className="h-full">
                <LightBrowser
                  url={activeTab.sourceData}
                  title={activeTab.title}
                  onLoadComplete={() => {
                    // 页面加载完成
                  }}
                  onError={() => {
                    // 页面加载错误处理
                  }}
                  onNavigateToNewTab={(newUrl) => {
                    // 在新标签页中打开链接
                    addTab({
                      title: new URL(newUrl).hostname,
                      sourceType: 'url',
                      sourceData: newUrl,
                      originalContent: '',
                      aiNoteMarkdown: '',
                      isLoading: false,
                      aiAnalyzing: true
                    })
                    // 后台处理新标签页的AI分析
                    const newTabId = useAppStore.getState().tabs[useAppStore.getState().tabs.length - 1]?.id
                    if (newTabId) {
                      processUrlInBackground(newUrl, newTabId)
                    }
                  }}
                />
              </div>
            ) : (
              // 对于文本类型，显示处理后的内容 - 修复滚动功能
              <div className="h-full flex flex-col">
                <div className="flex-1 overflow-y-auto overflow-x-hidden p-6">
                  <div className="max-w-4xl mx-auto">
                    <h1 className="text-2xl font-bold text-gray-900 mb-6">
                      {activeTab.title}
                    </h1>
                    <SafeMarkdown className="prose prose-lg max-w-none prose-headings:text-gray-800 prose-headings:font-semibold prose-p:text-gray-700 prose-p:leading-relaxed prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:my-1 prose-strong:text-gray-900 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:rounded prose-blockquote:border-l-4 prose-blockquote:border-blue-200 prose-blockquote:pl-4 prose-blockquote:italic prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline">
                      {activeTab.originalContent || activeTab.sourceData}
                    </SafeMarkdown>
                  </div>
                </div>
              </div>
            )
          ) : (
            // 空的新建标签页 - 直接使用WelcomeScreen组件
            <WelcomeScreen
              onSubmit={(input) => {
                setInputValue(input)

                // 直接调用处理逻辑
                const processedInput = input.trim()
                if (!processedInput) return

                setProcessing(true)

                // 检查是否为URL
                const isUrl = /^https?:\/\/.+/.test(processedInput) ||
                             /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}/.test(processedInput)

                if (isUrl) {
                  // 对于URL：立即创建标签页并显示浏览器，同时后台处理AI分析
                  const tabId = addTab({
                    title: new URL(processedInput.startsWith('http') ? processedInput : `https://${processedInput}`).hostname,
                    sourceType: 'url',
                    sourceData: processedInput.startsWith('http') ? processedInput : `https://${processedInput}`,
                    originalContent: '',
                    aiNoteMarkdown: '',
                    isLoading: false,
                    aiAnalyzing: true
                  })

                  setInputValue('')
                  setProcessing(false)

                  // 后台异步处理AI分析
                  processUrlInBackground(processedInput.startsWith('http') ? processedInput : `https://${processedInput}`, tabId)
                } else {
                  // 对于文本：使用流式处理
                  const tabId = addTab({
                    title: '文本内容',
                    sourceType: 'text',
                    sourceData: input,
                    originalContent: '',
                    aiNoteMarkdown: '',
                    isLoading: false,
                    aiAnalyzing: true
                  })

                  setInputValue('')
                  setProcessing(false)

                  // 后台流式处理文本内容
                  processContentWithStream(processedInput, 'text', tabId)
                }
              }}
              loading={useAppStore.getState().isProcessing}
            />
          )
        ) : (
          // 没有标签页时的欢迎界面
          <div className="flex items-center justify-center h-full">
            <div className="w-full max-w-2xl px-6">
              <div className="text-center mb-8">
                <p className="text-lg text-gray-600 mb-8">输入网页链接或文本内容，生成结构化笔记</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="relative">
                  <textarea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="粘贴网页链接或输入文本内容..."
                    className="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={useAppStore.getState().isProcessing}
                  />
                </div>

                <button
                  type="submit"
                  disabled={!inputValue.trim() || useAppStore.getState().isProcessing}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {useAppStore.getState().isProcessing ? '处理中...' : '开始分析'}
                </button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default WorkArea
